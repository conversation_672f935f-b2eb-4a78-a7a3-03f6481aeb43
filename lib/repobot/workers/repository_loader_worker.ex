defmodule Repobot.Workers.RepositoryLoaderWorker do
  @moduledoc """
  Oban worker for loading/refreshing user repositories from GitHub in the background.

  This worker handles the repository loading logic that was previously done synchronously
  in the onboarding flow, making it non-blocking and providing progress feedback.
  """

  use Repobot.Workers.Worker, queue: :sync, max_attempts: 3

  alias Repobot.{Repositories, Accounts.User, Accounts.Organization}

  @impl Oban.Worker
  def perform(
        %Oban.Job{args: %{"user_id" => user_id, "organization_id" => organization_id} = args} =
          job
      ) do
    log_job_start(job)

    # Get communication parameters
    topic = Map.get(args, "topic", "repository_loading:#{user_id}")
    receiver = Map.get(args, "receiver")

    try do
      # Load user and organization
      user = Repobot.Repo.get!(User, user_id)
      _organization = Repobot.Repo.get!(Organization, organization_id)

      # Send initial progress update
      send_progress_update(topic, receiver, 10, "Starting repository refresh...")

      # Check if repositories exist in cache
      cached_repos = Repositories.user_repositories(user, false, organization_id)

      if Enum.empty?(cached_repos) do
        # No cached repositories, need to refresh from GitHub
        send_progress_update(topic, receiver, 30, "Fetching repositories from GitHub...")

        case refresh_repositories(user, organization_id, topic, receiver) do
          {:ok, repositories} ->
            send_progress_update(topic, receiver, 90, "Repository refresh complete")

            # Send completion notification with repository count instead of full structs
            send_completion_notification(topic, receiver, %{
              status: :ok,
              repositories_count: length(repositories),
              repository_ids: Enum.map(repositories, & &1.id)
            })

            log_job_success(job, %{
              repositories_count: length(repositories),
              organization_id: organization_id
            })

            :ok

          {:error, reason} ->
            send_completion_notification(topic, %{status: :error, reason: reason})
            log_job_error(job, reason)
            {:error, reason}
        end
      else
        # Repositories already cached, return them quickly
        send_progress_update(topic, 90, "Using cached repositories")

        send_completion_notification(topic, %{
          status: :ok,
          repositories_count: length(cached_repos),
          repository_ids: Enum.map(cached_repos, & &1.id)
        })

        log_job_success(job, %{
          repositories_count: length(cached_repos),
          organization_id: organization_id,
          cached: true
        })

        :ok
      end
    rescue
      e ->
        reason = Exception.message(e)
        send_completion_notification(topic, %{status: :error, reason: reason})
        log_job_error(job, reason, %{exception: inspect(e)})
        {:error, reason}
    end
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)

    reason = "Invalid job arguments - missing required user_id or organization_id"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Public API for enqueuing repository loading jobs

  @doc """
  Enqueues a repository loading job for the given user and organization.

  ## Parameters
  - user_id: The ID of the user whose repositories to load
  - organization_id: The ID of the organization context
  - topic: Optional PubSub topic for progress updates (defaults to "repository_loading:<user_id>")

  ## Returns
  {:ok, %Oban.Job{}} on success, {:error, reason} on failure
  """
  def enqueue_repository_loading(user_id, organization_id, topic \\ nil) do
    args = %{
      "user_id" => user_id,
      "organization_id" => organization_id
    }

    args = if topic, do: Map.put(args, "topic", topic), else: args

    new(args)
    |> Oban.insert()
  end

  # Private helper functions

  defp refresh_repositories(user, organization_id, topic) do
    try do
      send_progress_update(topic, 40, "Connecting to GitHub API...")

      # Use the existing repository refresh logic
      repositories = Repositories.user_repositories(user, :refresh, organization_id)

      send_progress_update(topic, 80, "Processing #{length(repositories)} repositories...")

      {:ok, repositories}
    rescue
      e ->
        {:error, Exception.message(e)}
    end
  end

  defp send_progress_update(topic, progress, message) do
    # Use a safe atom conversion for the topic
    channel = String.to_existing_atom(topic)

    Oban.Notifier.notify(Oban, channel, %{
      event: :repository_loading_progress,
      progress: progress,
      message: message
    })
  rescue
    ArgumentError ->
      # If the atom doesn't exist, create it safely (only for known patterns)
      if String.starts_with?(topic, "repository_loading:") do
        channel = String.to_atom(topic)

        Oban.Notifier.notify(Oban, channel, %{
          event: :repository_loading_progress,
          progress: progress,
          message: message
        })
      end
  end

  defp send_completion_notification(topic, result) do
    # Use a safe atom conversion for the topic
    channel = String.to_existing_atom(topic)

    Oban.Notifier.notify(Oban, channel, %{
      event: :repository_loading_complete,
      result: result
    })
  rescue
    ArgumentError ->
      # If the atom doesn't exist, create it safely (only for known patterns)
      if String.starts_with?(topic, "repository_loading:") do
        channel = String.to_atom(topic)

        Oban.Notifier.notify(Oban, channel, %{
          event: :repository_loading_complete,
          result: result
        })
      end
  end
end
